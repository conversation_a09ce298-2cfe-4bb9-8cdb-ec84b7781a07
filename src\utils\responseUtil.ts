import { Response } from 'express';
import config from '@/config/config';
import { logger } from './logger';


// Response utility for consistent API responses
export const ResponseUtil = {
  success: (res: Response, message: string, result?: any, requestId?: string) => {

    if (requestId) {
      logger.logResponseSuccess(requestId, result, 200);
    }
    else
    {
      logger.error('Response success without request ID', result);
    }

    res.status(200).json({
      success: true,
      message,
      data: result?.data || result,
      pagination: result?.pagination,
      ...(config.NODE_ENV === 'development' && requestId ? { requestId } : {}),
    });
  },

  error: (res: Response, message: string, statusCode: number = 500, stack?: string, requestId?: string) => {
    if (requestId) {
      logger.logResponseError(requestId, message, stack);
    }
    else
    {
      logger.error('Response error without request ID', message);
    }

    res.status(statusCode).json({
      success: false,
      message,
      ...(config.NODE_ENV === 'development' && requestId ? { requestId } : {}),
      ...(config.NODE_ENV === 'development' && stack ? { stack } : {}),
    });
  },
};
