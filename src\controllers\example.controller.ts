import { Request, Response } from 'express';
import { logger } from '@/utils/logger';
import { ResponseUtil } from '@/utils/responseUtil';

// Request interface already extended globally in middleware

// Example controller to demonstrate logger usage
export const exampleController = {
  // Basic info logging example
  getInfo: (req: Request, res: Response) => {
    logger.info('Getting application info', {
      endpoint: '/info',
      method: req.method
    });

    ResponseUtil.success(res, 'Application info retrieved successfully', {
      name: 'RiteFit.AI Backend',
      version: '1.0.0',
      environment: process.env.NODE_ENV,
      timestamp: new Date().toISOString(),
    }, req.requestId);
  },

  // Example with detailed logging
  getDetailedExample: (req: Request, res: Response) => {
    logger.info('Processing detailed example request', {
      queryParams: req.query,
    });

    // Simulate some processing
    const processingData = {
      step1: 'Data validation',
      step2: 'Business logic processing',
      step3: 'Response preparation',
    };

    logger.info('Processing steps completed', processingData);

    ResponseUtil.success(res, 'Detailed example processed successfully', {
      processed: true,
      steps: processingData,
    }, req.requestId);
  },

  // Example error handling
  getErrorExample: (req: LoggedRequest, res: LoggedResponse) => {
    try {
      logger.info('Processing error example request');

      // Simulate an error
      throw new Error('This is a simulated error for demonstration');

    } catch (error) {
      logger.error('Error in example controller', error as Error);

      ResponseUtil.error(res, 'An error occurred while processing the request', 500,
        process.env.NODE_ENV === 'development' ? (error as Error).stack : undefined
      );
    }
  },

  // Example with warning
  getWarningExample: (req: LoggedRequest, res: LoggedResponse) => {
    logger.warning('Warning example endpoint accessed', {
      userAgent: req.headers['user-agent'],
      ip: req.ip,
    });

    ResponseUtil.success(res, 'Warning example completed', {
      data: {
        warning: 'This endpoint is deprecated and will be removed in future versions',
      }
    });
  },

  // Example POST request with body logging
  postExample: (req: LoggedRequest, res: LoggedResponse) => {
    logger.info('Processing POST example', {
      bodySize: JSON.stringify(req.body).length,
      contentType: req.headers['content-type'],
    });

    // Validate request body
    if (!req.body || Object.keys(req.body).length === 0) {
      logger.warning('Empty request body received');
      return ResponseUtil.error(res, 'Request body is required', 400);
    }

    logger.info('Request body validated successfully', {
      fields: Object.keys(req.body),
    });

    ResponseUtil.success(res, 'POST request processed successfully', {
      data: {
        received: req.body,
        processed: true,
      }
    });
  },
};
