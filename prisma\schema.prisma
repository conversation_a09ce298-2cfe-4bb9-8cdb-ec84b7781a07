// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// User model for authentication and user management
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String?  @unique
  firstName String?
  lastName  String?
  password  String
  isActive  Boolean  @default(true)
  role      UserRole @default(USER)
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  sessions  Session[]
  profile   UserProfile?
  
  @@map("users")
}

// User profile for additional user information
model UserProfile {
  id       String  @id @default(cuid())
  userId   String  @unique
  bio      String?
  avatar   String?
  phone    String?
  address  String?
  city     String?
  country  String?
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("user_profiles")
}

// Session model for JWT token management
model Session {
  id           String    @id @default(cuid())
  userId       String
  token        String    @unique
  refreshToken String?   @unique
  expiresAt    DateTime
  isActive     Boolean   @default(true)
  
  // Device/Client info
  userAgent    String?
  ipAddress    String?
  
  // Timestamps
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  
  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("sessions")
}

// Notification model for system notifications
model Notification {
  id        String             @id @default(cuid())
  title     String
  message   String
  type      NotificationType   @default(INFO)
  status    NotificationStatus @default(UNREAD)
  userId    String?
  
  // Metadata
  metadata  Json?
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  readAt    DateTime?
  
  @@map("notifications")
}

// Enums
enum UserRole {
  ADMIN
  USER
  MODERATOR
}

enum NotificationType {
  INFO
  WARNING
  ERROR
  SUCCESS
}

enum NotificationStatus {
  UNREAD
  READ
  ARCHIVED
}
