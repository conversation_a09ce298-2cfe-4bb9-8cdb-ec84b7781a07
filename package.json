{"name": "ritefit-backend", "version": "1.0.0", "description": "Scalable backend API for RiteFit.AI using Node.js, Express.js, Prisma, and MySQL", "main": "server.js", "scripts": {"start": "node dist/server.js", "dev": "nodemon --exec \"ts-node -r tsconfig-paths/register src/server.ts\"", "build": "tsc", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "type-check": "tsc --noEmit", "prisma:generate": "prisma generate", "prisma:push": "prisma db push", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "prisma:reset": "prisma migrate reset", "prisma:deploy": "prisma migrate deploy", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "audit": "npm audit", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["nodejs", "express", "prisma", "mysql", "api", "backend"], "author": "RiteFit.AI Team", "license": "ISC", "type": "commonjs", "dependencies": {"@prisma/client": "^6.9.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "helmet": "^8.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "mysql2": "^3.14.1", "prisma": "^6.9.0", "tsconfig-paths": "^4.2.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.25.56"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.10", "@types/node": "^22.15.30", "@types/winston": "^2.4.4", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "nodemon": "^3.1.10", "prettier": "^3.5.3", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}