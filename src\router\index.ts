import { Router, Application, Request, Response } from 'express';
import { ResponseUtil } from '@/utils/response-util';

// Create main API router
const createApiRouter = (): Router => {
  const apiRouter = Router();

  // Health check for API
  apiRouter.get('/', (_req: Request, res: Response) => {
    ResponseUtil.success(res, 'RiteFit.AI Backend API', {
      version: '1.0.0',
      timestamp: new Date().toISOString(),
    });
  });

  // TODO: Register other module routes here

  return apiRouter;
};

// Setup all application routes
export const setupRoutes = (app: Application): void => {
  // Global health check endpoint
  app.get('/health', (_req: Request, res: Response) => {
    ResponseUtil.success(res, 'Server is running', {
      version: '1.0.0',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
    });
  });

  // API routes
  const apiRouter = createApiRouter();
  app.use('/api', apiRouter);

};

export default setupRoutes;
