import { Router, Application, Request, Response } from 'express';

// Create main API router
const createApiRouter = (): Router => {
  const apiRouter = Router();

  // Health check for API
  apiRouter.get('/', (_req: Request, res: Response) => {
    res.status(200).json({
      success: true,
      message: 'RiteFit.AI Backend API',
      version: '1.0.0',
      timestamp: new Date().toISOString(),
      endpoints: {
        health: '/health',
        auth: '/api/auth',
        users: '/api/users',
        notifications: '/api/notifications',
        examples: '/api/examples',
      },
    });
  });


  // TODO: Register other module routes here

  return apiRouter;
};

// Setup all application routes
export const setupRoutes = (app: Application): void => {
  // Global health check endpoint
  app.get('/health', (_req: Request, res: Response) => {
    res.status(200).json({
      success: true,
      message: 'Server is running',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
    });
  });

  // API routes
  const apiRouter = createApiRouter();
  app.use('/api', apiRouter);

};

export default setupRoutes;
